import HttpClient from "../http/HttpClient";
import FlavorsType from "../interfaces/FlavorsType";
import { PaginacaoType } from "../interfaces/PaginacaoType";

export default class FlavorsService {
    async listar(page: number = 0, size: number = 6): Promise<PaginacaoType<FlavorsType>> {
        const response = await HttpClient.get<PaginacaoType<FlavorsType>>(`sabor-cultura/web/listar?page=${page}&size=${size}`);
        return response;
    }
}