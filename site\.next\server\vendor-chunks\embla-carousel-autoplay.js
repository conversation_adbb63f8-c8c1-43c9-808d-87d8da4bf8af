"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-autoplay";
exports.ids = ["vendor-chunks/embla-carousel-autoplay"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Autoplay)\n/* harmony export */ });\nconst defaultOptions = {\n  active: true,\n  breakpoints: {},\n  delay: 4000,\n  jump: false,\n  playOnInit: true,\n  stopOnFocusIn: true,\n  stopOnInteraction: true,\n  stopOnMouseEnter: false,\n  stopOnLastSnap: false,\n  rootNode: null\n};\n\nfunction normalizeDelay(emblaApi, delay) {\n  const scrollSnaps = emblaApi.scrollSnapList();\n  if (typeof delay === 'number') {\n    return scrollSnaps.map(() => delay);\n  }\n  return delay(scrollSnaps, emblaApi);\n}\nfunction getAutoplayRootNode(emblaApi, rootNode) {\n  const emblaRootNode = emblaApi.rootNode();\n  return rootNode && rootNode(emblaRootNode) || emblaRootNode;\n}\n\nfunction Autoplay(userOptions = {}) {\n  let options;\n  let emblaApi;\n  let destroyed;\n  let delay;\n  let timerStartTime = null;\n  let timerId = 0;\n  let autoplayActive = false;\n  let mouseIsOver = false;\n  let playOnDocumentVisible = false;\n  let jump = false;\n  function init(emblaApiInstance, optionsHandler) {\n    emblaApi = emblaApiInstance;\n    const {\n      mergeOptions,\n      optionsAtMedia\n    } = optionsHandler;\n    const optionsBase = mergeOptions(defaultOptions, Autoplay.globalOptions);\n    const allOptions = mergeOptions(optionsBase, userOptions);\n    options = optionsAtMedia(allOptions);\n    if (emblaApi.scrollSnapList().length <= 1) return;\n    jump = options.jump;\n    destroyed = false;\n    delay = normalizeDelay(emblaApi, options.delay);\n    const {\n      eventStore,\n      ownerDocument\n    } = emblaApi.internalEngine();\n    const isDraggable = !!emblaApi.internalEngine().options.watchDrag;\n    const root = getAutoplayRootNode(emblaApi, options.rootNode);\n    eventStore.add(ownerDocument, 'visibilitychange', visibilityChange);\n    if (isDraggable) {\n      emblaApi.on('pointerDown', pointerDown);\n    }\n    if (isDraggable && !options.stopOnInteraction) {\n      emblaApi.on('pointerUp', pointerUp);\n    }\n    if (options.stopOnMouseEnter) {\n      eventStore.add(root, 'mouseenter', mouseEnter);\n    }\n    if (options.stopOnMouseEnter && !options.stopOnInteraction) {\n      eventStore.add(root, 'mouseleave', mouseLeave);\n    }\n    if (options.stopOnFocusIn) {\n      emblaApi.on('slideFocusStart', stopAutoplay);\n    }\n    if (options.stopOnFocusIn && !options.stopOnInteraction) {\n      eventStore.add(emblaApi.containerNode(), 'focusout', startAutoplay);\n    }\n    if (options.playOnInit) startAutoplay();\n  }\n  function destroy() {\n    emblaApi.off('pointerDown', pointerDown).off('pointerUp', pointerUp).off('slideFocusStart', stopAutoplay);\n    stopAutoplay();\n    destroyed = true;\n    autoplayActive = false;\n  }\n  function setTimer() {\n    const {\n      ownerWindow\n    } = emblaApi.internalEngine();\n    ownerWindow.clearTimeout(timerId);\n    timerId = ownerWindow.setTimeout(next, delay[emblaApi.selectedScrollSnap()]);\n    timerStartTime = new Date().getTime();\n    emblaApi.emit('autoplay:timerset');\n  }\n  function clearTimer() {\n    const {\n      ownerWindow\n    } = emblaApi.internalEngine();\n    ownerWindow.clearTimeout(timerId);\n    timerId = 0;\n    timerStartTime = null;\n    emblaApi.emit('autoplay:timerstopped');\n  }\n  function startAutoplay() {\n    if (destroyed) return;\n    if (documentIsHidden()) {\n      playOnDocumentVisible = true;\n      return;\n    }\n    if (!autoplayActive) emblaApi.emit('autoplay:play');\n    setTimer();\n    autoplayActive = true;\n  }\n  function stopAutoplay() {\n    if (destroyed) return;\n    if (autoplayActive) emblaApi.emit('autoplay:stop');\n    clearTimer();\n    autoplayActive = false;\n  }\n  function visibilityChange() {\n    if (documentIsHidden()) {\n      playOnDocumentVisible = autoplayActive;\n      return stopAutoplay();\n    }\n    if (playOnDocumentVisible) startAutoplay();\n  }\n  function documentIsHidden() {\n    const {\n      ownerDocument\n    } = emblaApi.internalEngine();\n    return ownerDocument.visibilityState === 'hidden';\n  }\n  function pointerDown() {\n    if (!mouseIsOver) stopAutoplay();\n  }\n  function pointerUp() {\n    if (!mouseIsOver) startAutoplay();\n  }\n  function mouseEnter() {\n    mouseIsOver = true;\n    stopAutoplay();\n  }\n  function mouseLeave() {\n    mouseIsOver = false;\n    startAutoplay();\n  }\n  function play(jumpOverride) {\n    if (typeof jumpOverride !== 'undefined') jump = jumpOverride;\n    startAutoplay();\n  }\n  function stop() {\n    if (autoplayActive) stopAutoplay();\n  }\n  function reset() {\n    if (autoplayActive) startAutoplay();\n  }\n  function isPlaying() {\n    return autoplayActive;\n  }\n  function next() {\n    const {\n      index\n    } = emblaApi.internalEngine();\n    const nextIndex = index.clone().add(1).get();\n    const lastIndex = emblaApi.scrollSnapList().length - 1;\n    const kill = options.stopOnLastSnap && nextIndex === lastIndex;\n    if (emblaApi.canScrollNext()) {\n      emblaApi.scrollNext(jump);\n    } else {\n      emblaApi.scrollTo(0, jump);\n    }\n    emblaApi.emit('autoplay:select');\n    if (kill) return stopAutoplay();\n    startAutoplay();\n  }\n  function timeUntilNext() {\n    if (!timerStartTime) return null;\n    const currentDelay = delay[emblaApi.selectedScrollSnap()];\n    const timePastSinceStart = new Date().getTime() - timerStartTime;\n    return currentDelay - timePastSinceStart;\n  }\n  const self = {\n    name: 'autoplay',\n    options: userOptions,\n    init,\n    destroy,\n    play,\n    stop,\n    reset,\n    isPlaying,\n    timeUntilNext\n  };\n  return self;\n}\nAutoplay.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel-autoplay.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js\n");

/***/ })

};
;