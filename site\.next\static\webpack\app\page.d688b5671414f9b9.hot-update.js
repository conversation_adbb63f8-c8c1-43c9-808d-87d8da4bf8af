"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/http/HttpClient.ts":
/*!************************************!*\
  !*** ./src/lib/http/HttpClient.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst baseUrl = \"http://localhost:3002\";\nconst handleResponse = async (response)=>{\n    if (!response.ok) {\n        let errorMessage = \"Erro desconhecido. Por favor, tente novamente.\";\n        try {\n            const errorData = await response.json();\n            if (errorData.erro) {\n                errorMessage = errorData.erro;\n            } else if (errorData.message) {\n                errorMessage = errorData.message;\n            }\n        } catch (e) {\n            errorMessage = \"Erro desconhecido. Por favor, tente novamente.\";\n        }\n        const error = {\n            message: errorMessage,\n            status: response.status\n        };\n        throw error;\n    }\n    return await response.json();\n};\nconst HttpClient = {\n    get: async (path)=>{\n        const response = await fetch(baseUrl + path, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        return handleResponse(response);\n    },\n    post: async (path, body)=>{\n        const response = await fetch(baseUrl + path, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(body)\n        });\n        return handleResponse(response);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HttpClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/http/HttpClient.ts\n"));

/***/ })

});