"use client";

import { useEffect, useState } from "react";
import Container from "@/components/Container";
import SectionTitle from "@/components/sections/SectionTitle";
import Flavors from "@/components/Flavors";
import FadeInOnScroll from "@/components/animation/FadeInOnScroll";
import CardSkeleton from "@/components/CardSkeleton";
import FilterNotFound from "@/components/FilterNotFound";
import InfiniteScrollWithPagination from "@/components/InfiniteScrollWithPagination";
import Filter from "@/components/Filter";
import Modal from "@/components/Modal";
import DetailsFlavors from "@/components/DetailsFlavors";
import FlavorsType from "@/lib/interfaces/FlavorsType";
import FlavorsService from "@/lib/service/FlavorsService";
import useInfiniteScroll from "@/hooks/useInfiniteScroll";

export default function Sabores() {
    const [filteredFlavors, setFilteredFlavors] = useState<FlavorsType[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<string>("");
    const [selectedFlavors, setSelectedFlavors] = useState<FlavorsType>({} as FlavorsType);
    const [modalOpen, setModalOpen] = useState(false);
    const [municipalities, setMunicipalities] = useState<string[]>([]);

    const {
        items: flavors,
        loading,
        hasMore,
        loadMore,
        refresh,
    } = useInfiniteScroll({
        fetchFunction: async (page: number, size: number) => {
            const service = new FlavorsService();
            return await service.listar(page, size);
        },
        initialPageSize: 6,
    });

    // Atualizar municípios quando flavors carregar
    useEffect(() => {
        const uniqueMunicipalities = Array.from(new Set(flavors.map(flavor => flavor.municipio.nome)));
        setMunicipalities(uniqueMunicipalities);
    }, [flavors]);

    // Filtrar flavors por município
    useEffect(() => {
        if (selectedCategory === "") {
            setFilteredFlavors(flavors);
        } else {
            const filtered = flavors.filter(flavor => flavor.municipio.nome === selectedCategory);
            setFilteredFlavors(filtered);
        }
    }, [selectedCategory, flavors]);

    const handleFlavorClick = (id: string) => {
        const selected = flavors.find((item) => item.id === id);
        if (selected) {
            setSelectedFlavors(selected);
            setModalOpen(true);
        }
    };

    const handleCategoryChange = (category: string) => {
        setSelectedCategory(category);
        // Se aplicar filtro, não carregamos mais páginas (limitação do filtro client-side)
        // Para filtro server-side, você modificaria o fetchFunction no hook
    };

    return (
        <div>
            <div className="flex flex-col items-center justify-center py-10 px-4">
                <SectionTitle subtitle="Descubra os sabores autênticos e a rica cultura que fazem da nossa região um lugar único e especial.">
                    Sabores & Cultura
                </SectionTitle>
            </div>

            <Container>
                <div className="py-8">
                    <FadeInOnScroll direction="up" delay={0.2}>
                        <Filter
                            filters={municipalities}
                            selectedFilter={selectedCategory}
                            onFilterChange={handleCategoryChange}
                        />
                    </FadeInOnScroll>

                    {/* Se houver filtro ativo, não usamos paginação infinita */}
                    {selectedCategory ? (
                        <div className="py-8">
                            {filteredFlavors.length === 0 ? (
                                <div className="flex justify-center items-center p-10">
                                    <FilterNotFound />
                                </div>
                            ) : (
                                <div className="grid mt-8 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6">
                                    {filteredFlavors.map((flavor, index) => (
                                        <FadeInOnScroll
                                            key={flavor.id}
                                            direction="up"
                                            delay={0.2 + (index % 6) * 0.1}
                                            className="flex justify-center transition-all duration-300 transform hover:-translate-y-2"
                                        >
                                            <button
                                                onClick={() => handleFlavorClick(flavor.id)}
                                                className="w-full h-full flex justify-center items-center"
                                                type="button"
                                            >
                                                <Flavors flavors={flavor} />
                                            </button>
                                        </FadeInOnScroll>
                                    ))}
                                </div>
                            )}
                        </div>
                    ) : (
                        <InfiniteScrollWithPagination
                            items={flavors}
                            loading={loading}
                            hasMore={hasMore}
                            onLoadMore={loadMore}
                        >
                            {(items) =>
                                loading && items.length === 0 ? (
                                    <div className="grid mt-8 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6">
                                        {Array.from({ length: 6 }).map((_, index) => (
                                            <CardSkeleton key={index} />
                                        ))}
                                    </div>
                                ) : items.length === 0 ? (
                                    <div className="flex justify-center items-center p-10">
                                        <FilterNotFound />
                                    </div>
                                ) : (
                                    <div className="grid mt-8 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6">
                                        {items.map((flavor, index) => (
                                            <FadeInOnScroll
                                                key={flavor.id}
                                                direction="up"
                                                delay={0.2 + (index % 6) * 0.1}
                                                className="flex justify-center transition-all duration-300 transform hover:-translate-y-2"
                                            >
                                                <button
                                                    onClick={() => handleFlavorClick(flavor.id)}
                                                    className="w-full h-full flex justify-center items-center"
                                                    type="button"
                                                >
                                                    <Flavors flavors={flavor} />
                                                </button>
                                            </FadeInOnScroll>
                                        ))}
                                    </div>
                                )
                            }
                        </InfiniteScrollWithPagination>
                    )}

                    <Modal
                        isOpen={modalOpen}
                        onClose={() => setModalOpen(false)}
                        title="Detalhes do Sabores & Cultura"
                    >
                        <DetailsFlavors flavors={selectedFlavors} />
                    </Modal>
                </div>
            </Container>
        </div>
    );
}