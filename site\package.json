{"name": "turismo_front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3002", "build": "next build --no-lint", "start": "next start --port 3002", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.18.1", "leaflet": "^1.9.4", "lucide-react": "^0.518.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/leaflet": "^1.9.18", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tailwindcss-animated": "^2.0.0", "tw-animate-css": "^1.3.4", "typescript": "^5"}}